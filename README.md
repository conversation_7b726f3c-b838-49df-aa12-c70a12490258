# Judging Monitoring System

A real-time judging monitoring system built with Next.js 14, Tailwind CSS, and S<PERSON> for data fetching.

## Features

- Real-time monitoring of judging progress
- Filtering by category and round
- Configurable auto-refresh intervals
- Sequential JID break detection with visual alerts
- Toggle for showing/hiding time columns
- Authentication protection
- Light and dark mode support

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- MySQL 8.0 or later

### Installation

1. Clone the repository
2. Install dependencies
   ```bash
   npm install
   ```
3. Copy the example environment file and update with your settings
   ```bash
   cp .env.local.example .env.local
   ```
4. Update the `.env.local` file with your database credentials and event ID

### Running the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Usage

1. Log in using the credentials specified in your `.env.local` file
2. Navigate to the Judging Monitor page from the dashboard or directly at `/judging`
3. Use the filters to narrow down the data by category and round
4. Set an auto-refresh interval or use the manual refresh button
5. Toggle the "Show Time" checkbox to show or hide time columns

## Database Schema

The application uses the following tables from the database:

- `events_judging` - Records of judging activity
- `events_juries` - Information about judges
- `events_circuits` - Categories for judging
- `events_rounds` - Rounds of judging
- `events_submissions` - Submissions being judged

## Project Structure

- `/src/app/judging` - Judging page and API routes
- `/src/components/judging-monitor.tsx` - Main judging monitor component
- `/src/lib/data.ts` - Data fetching and transformation functions
- `/src/lib/db.ts` - Database connection utilities
