Act as a senior full-stack developer specializing in Next.js 14, Tailwind CSS, and real-time data visualization. Your task is to build a judging monitoring page for an existing Next.js application.

**CRITICAL FIRST STEP:** Before writing any code, you must first generate a detailed, step-by-step plan for how you will build this feature. Present this plan in a markdown-formatted file named `PLAN.md`. The plan should contain a checklist of tasks. I will then approve the plan and ask you to execute each task one by one.

---

### Project Overview & Goal

The goal is to create a page at `/judging` that displays a table of judging progress in near real-time. This page should be protected by the existing authentication.

### Key Requirements & Context

**1. Data Source & Schema:**
The data comes from a MySQL v8 database. Here is the relevant schema from `database.sql`:
The data must be filtered by an `event_id` which will be provided in a `.env.local` file as `EVENT_ID=123`.

**2. Core UI: The Judging Table**
- The main component will be a data table.
- **Columns:** The first column is "NO" for a numbered list. Subsequent columns represent each judge (e.g., "JURY-A", "JURY-B"), fetched from the `events_juries` table.
- **Sub-Columns:** Each judge's column is split into two sub-columns: "JID" and "TIME".
- **Rows:** The rows represent the sequence of submissions judged. The table should be able to handle cases where one judge has judged more submissions than another.

**3. Data Transformation Logic:**
The database query will return a flat list of judging records. You will need to write a function to transform this list into a nested data structure suitable for rendering the table. For example, group the records by submission number across all judges.

**4. Key Logic: Sequential JID Alert:**
- For each judge's column, the `jid` values are expected to be sequential (e.g., 38, 39, 40).
- If a `jid` breaks the sequence (e.g., the previous `jid` was 39 and the current is 41), the table cell for `jid` 41 should have a visually distinct alert style (e.g., a red background).

**5. UI Controls & Features:**
- **File Location:** The main page should be at `app/judging/page.tsx`.
- **Component Structure:** The table and its controls should be a client component (`"use client"`) located at `components/judging-monitor.tsx`. The data fetching logic should be in a separate file, e.g., `lib/data.ts`.
- **Filtering:** Add dropdowns/selects to filter the data by "Category" and "Round". These values can be fetched from the `events_circuits` table.
- **Refresh Control:**
    - A dropdown to select an auto-refresh interval (e.g., 3s, 5s, 10s, Off).
    - A manual "Refresh" button.
    - **Implementation:** I recommend using a library like `SWR` or `TanStack Query` for data fetching, as they have built-in support for polling/intervals.
- **Column Visibility:** A checkbox labeled "Show Time". When unchecked, the "TIME" sub-columns for all judges should be hidden.
- **Styling:** Use Tailwind CSS and ensure the UI works in both light and dark modes. Use a component library like Shadcn/UI if possible to accelerate UI development.

---

**REMINDER:** Your first output should ONLY be the `PLAN.md` file with the checklist of tasks. Do not write any `.tsx` or `.ts` code yet.