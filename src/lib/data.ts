import 'server-only';
import { executeQuery } from './db';

// Types for the data structures
export interface Judge {
  id: number;
  name: string;
  code: string;
}

export interface JudgingRecord {
  id: number;
  circuit_id: number;
  jury_id: number;
  round_id: number;
  submission_id: number;
  jid: number;
  jury_code: string;
  jury_name: string;
  time_created: number;
  category_id: number;
  category_name: string;
}

export interface Circuit {
  id: number;
  name: string;
}

export interface Category {
  id: number;
  event_id: number;
  code: string;
  name: string;
  description: string | null;
  has_special_award: 'yes' | 'no';
  type: 'digital' | 'print';
  max_submit: number;
  position: number;
}

export interface Round {
  id: number;
  name: string;
  position: number;
}

export interface TransformedJudgingData {
  judges: Judge[];
  rows: {
    [key: string]: { // Key is a combination of judge_id and position
      jid: number;
      time: string;
      isSequenceBreak: boolean;
    };
  }[];
}

// Get all judges for an event, optionally filtered by category
export async function getJudges(eventId: number, categoryId?: number): Promise<Judge[]> {
  let query = `
    SELECT DISTINCT j.id, j.name, j.code 
    FROM events_juries j
  `;

  if (categoryId) {
    query += `
      JOIN events_categories_jury ecj ON j.id = ecj.jury_id
      JOIN events_circuits c ON ecj.circuit_id = c.id
      WHERE j.event_id = ? AND ecj.category_id = ?
    `;
  } else {
    query += ` WHERE j.event_id = ?`;
  }
  
  query += ` ORDER BY j.code ASC`;
  
  const params = categoryId ? [eventId, categoryId] : [eventId];
  return executeQuery<Judge[]>(query, params);
}

// Get all circuits (categories) for an event
export async function getCircuits(eventId: number): Promise<Circuit[]> {
  const query = `
    SELECT id, name 
    FROM events_circuits 
    WHERE event_id = ? 
    ORDER BY name ASC
  `;
  
  return executeQuery<Circuit[]>(query, [eventId]);
}

// Get all rounds for an event
export async function getRounds(eventId: number): Promise<Round[]> {
  const query = `
    SELECT id, name, position 
    FROM events_rounds 
    WHERE event_id = ? 
    ORDER BY position ASC
  `;
  
  return executeQuery<Round[]>(query, [eventId]);
}

// Get all categories for an event
export async function getCategories(eventId: number): Promise<Category[]> {
  const query = `
    SELECT id, event_id, code, name, description, has_special_award, type, max_submit, position
    FROM events_categories 
    WHERE event_id = ? 
    ORDER BY position ASC, code ASC
  `;
  
  return executeQuery<Category[]>(query, [eventId]);
}

// Get judging records with filters
export async function getJudgingRecords(
  eventId: number,
  categoryId?: number,
  roundId?: number
): Promise<JudgingRecord[]> {
  let query = `
    SELECT 
      ej.id,
      ej.circuit_id,
      ej.jury_id,
      ej.round_id,
      ej.submission_id,
      es.jid,
      j.code as jury_code,
      j.name as jury_name,
      ej.time_created,
      es.category_id,
      ec.name as category_name
    FROM 
      events_judging ej
    JOIN 
      events_juries j ON ej.jury_id = j.id
    JOIN 
      events_submissions es ON ej.submission_id = es.id
    JOIN 
      events_categories ec ON es.category_id = ec.id
    JOIN 
      events_circuits c ON ej.circuit_id = c.id
    WHERE 
      j.event_id = ?
  `;

  const params: (number | undefined)[] = [eventId];

  if (categoryId) {
    query += ` AND es.category_id = ?`;
    params.push(categoryId);
  }

  if (roundId) {
    query += ` AND ej.round_id = ?`;
    params.push(roundId);
  }

  query += ` ORDER BY ej.id DESC LIMIT 50`;

  return executeQuery<JudgingRecord[]>(query, params);
}

// Transform judging records into a format suitable for the table
export function transformJudgingData(records: JudgingRecord[], judges: Judge[]): TransformedJudgingData {
  // Create a map to track the last JID for each judge
  const lastJidMap = new Map<number, number>();
  
  // Group records by judge and sort by jid
  const judgeRecordsMap = new Map<number, JudgingRecord[]>();
  
  records.forEach(record => {
    if (!judgeRecordsMap.has(record.jury_id)) {
      judgeRecordsMap.set(record.jury_id, []);
    }
    judgeRecordsMap.get(record.jury_id)?.push(record);
  });
  
  // Sort records for each judge by jid
  judgeRecordsMap.forEach((judgeRecords, judgeId) => {
    judgeRecordsMap.set(judgeId, judgeRecords.sort((a, b) => a.jid - b.jid));
  });
  
  // Find the maximum number of records for any judge
  let maxRecords = 0;
  judgeRecordsMap.forEach(judgeRecords => {
    maxRecords = Math.max(maxRecords, judgeRecords.length);
  });
  
  // Create rows for the table
  const rows = [];
  
  for (let i = 0; i < maxRecords; i++) {
    const row: {
      [key: string]: {
        jid: number;
        time: string;
        isSequenceBreak: boolean;
      };
    } = {};
    
    judges.forEach(judge => {
      const judgeRecords = judgeRecordsMap.get(judge.id) || [];
      
      if (i < judgeRecords.length) {
        const record = judgeRecords[i];
        const lastJid = lastJidMap.get(judge.id) || 0;
        const isSequenceBreak = lastJid > 0 && record.jid !== lastJid + 1;
        
        row[`${judge.id}`] = {
          jid: record.jid,
          time: formatTimestamp(record.time_created),
          isSequenceBreak
        };
        
        lastJidMap.set(judge.id, record.jid);
      }
    });
    
    rows.push(row);
  }
  
  return {
    judges,
    rows
  };
}

// Helper function to format timestamp
function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp * 1000);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}