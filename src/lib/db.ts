import mysql from 'mysql2/promise';
import { cache } from 'react';

// Define types for database connection and results
type DbConnection = mysql.PoolConnection;
type QueryResults = mysql.RowDataPacket[] | mysql.RowDataPacket[][] | mysql.OkPacket | mysql.OkPacket[] | mysql.ResultSetHeader;

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'judging_db',
  port: parseInt(process.env.DB_PORT || "3306", 10),
};

// Create a connection pool
const pool = mysql.createPool(dbConfig);

// Cached function to get a database connection
export const getConnection = cache(async () => {
  try {
    // Get a connection from the pool
    const connection = await pool.getConnection();
    return connection;
  } catch (error) {
    console.error('Error getting database connection:', error);
    throw new Error('Failed to connect to database');
  }
});

// Execute a query with parameters
export async function executeQuery<T = QueryResults>(query: string, params: (string | number | undefined)[] = []): Promise<T> {
  let connection: DbConnection | undefined;
  try {
    connection = await getConnection();
    const [results] = await connection.execute(query, params);
    return results as T;
  } catch (error) {
    console.error('Database query error:', error);
    throw new Error('Database query failed');
  } finally {
    if (connection) connection.release();
  }
}