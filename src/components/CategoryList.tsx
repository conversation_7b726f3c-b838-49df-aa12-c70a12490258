'use client';

import { useEffect, useState } from 'react';
import { Category } from '@/lib/data';

export default function CategoryList() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/categories');
        
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        
        const data = await response.json();
        setCategories(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return <div className="p-4">Loading categories...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Categories</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category) => (
          <div 
            key={category.id} 
            className="border rounded-lg p-4 bg-white dark:bg-gray-800 shadow-sm"
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-bold text-lg">{category.name}</h3>
              <span className="bg-gray-200 dark:bg-gray-700 text-xs px-2 py-1 rounded">
                {category.code}
              </span>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Type: {category.type.charAt(0).toUpperCase() + category.type.slice(1)}
            </div>
            {category.description && (
              <p className="text-sm mt-2 text-gray-700 dark:text-gray-300">
                {category.description.length > 150 
                  ? `${category.description.substring(0, 150)}...` 
                  : category.description}
              </p>
            )}
            <div className="mt-3 flex justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Max Submissions: {category.max_submit}</span>
              <span>
                {category.has_special_award === 'yes' ? 'Special Award Available' : ''}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}