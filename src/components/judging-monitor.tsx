'use client';

import React, { useState, useEffect, Fragment } from 'react';
import useS<PERSON> from 'swr';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { RefreshCw } from 'lucide-react';

// Define types for the component props and state
interface JudgeData {
  id: number;
  name: string;
  code: string;
}

interface JudgingRecordData {
  jid: number;
  time: string;
  isSequenceBreak: boolean;
}

interface JudgingData {
  judges: JudgeData[];
  rows: Array<{
    [key: string]: JudgingRecordData;
  }>;
}

interface JudgingMonitorProps {
  eventId: number;
  initialCategories: Array<{ 
    id: number; 
    name: string; 
    code: string;
    description: string | null;
    has_special_award: 'yes' | 'no';
    type: 'digital' | 'print';
    max_submit: number;
    position: number;
  }>;
  initialRounds: Array<{ id: number; name: string }>;
  initialJudges: JudgeData[];
  initialJudgingData: JudgingData;
}

// Refresh interval options in milliseconds
const REFRESH_INTERVALS = [
  { label: 'Off', value: 0 },
  { label: '3s', value: 3000 },
  { label: '5s', value: 5000 },
  { label: '10s', value: 10000 },
];

export default function JudgingMonitor({
  eventId,
  initialCategories,
  initialRounds,
  initialJudges,
  initialJudgingData,
}: JudgingMonitorProps) {
  // State for filters and UI controls
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedRound, setSelectedRound] = useState<string>('');
  const [refreshInterval, setRefreshInterval] = useState<number>(0); // Default to off
  const [showTime, setShowTime] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Fetch data using SWR with the selected filters
  const { data, error, mutate } = useSWR<JudgingData>(
    `/api/judging?eventId=${eventId}${selectedCategory ? `&categoryId=${selectedCategory}` : ''}${selectedRound ? `&roundId=${selectedRound}` : ''}`,
    async (url: string) => {
      setIsLoading(true);
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        return data;
      } catch (error) {
        console.error('Error fetching judging data:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    {
      refreshInterval: refreshInterval,
      fallbackData: initialJudgingData,
      revalidateOnFocus: false,
    }
  );

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsLoading(true);
    await mutate();
    setIsLoading(false);
  };

  // Handle category selection change
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value === 'all' ? '' : value);
  };

  // Handle round selection change
  const handleRoundChange = (value: string) => {
    setSelectedRound(value === 'all' ? '' : value);
  };

  // Handle refresh interval change
  const handleRefreshIntervalChange = (value: string) => {
    setRefreshInterval(parseInt(value));
  };

  // Handle show time toggle
  const handleShowTimeChange = (checked: boolean) => {
    setShowTime(checked);
  };

  // Render the judging monitor UI
  return (
    <div className="w-full space-y-4">
      {/* Filters and controls */}
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-end">
        <div className="space-y-2 w-full md:w-auto">
          <label className="text-sm font-medium">Category</label>
          <Select
            value={selectedCategory || 'all'}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {initialCategories.map((category) => (
                <SelectItem key={category.id} value={category.id.toString()}>
                  {category.code} - {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 w-full md:w-auto">
          <label className="text-sm font-medium">Round</label>
          <Select
            value={selectedRound || 'all'}
            onValueChange={handleRoundChange}
          >
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="All Rounds" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Rounds</SelectItem>
              {initialRounds.map((round) => (
                <SelectItem key={round.id} value={round.id.toString()}>
                  {round.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 w-full md:w-auto">
          <label className="text-sm font-medium">Auto Refresh</label>
          <Select
            value={refreshInterval.toString()}
            onValueChange={handleRefreshIntervalChange}
          >
            <SelectTrigger className="w-full md:w-[120px]">
              <SelectValue placeholder="Off" />
            </SelectTrigger>
            <SelectContent>
              {REFRESH_INTERVALS.map((interval) => (
                <SelectItem key={interval.value} value={interval.value.toString()}>
                  {interval.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2 mt-6 md:mt-0">
          <Checkbox
            id="show-time"
            checked={showTime}
            onCheckedChange={handleShowTimeChange}
          />
          <label
            htmlFor="show-time"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Show Time
          </label>
        </div>

        <Button
          onClick={handleRefresh}
          className="ml-auto"
          disabled={isLoading}
        >
          <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Judging table */}
      <div className="rounded-lg border bg-card">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-transparent">
              <TableHead className="h-auto w-[80px]">
                <div className="text-center font-medium">NO</div>
              </TableHead>
              {(data?.judges || initialJudges).map((judge) => (
                <TableHead key={judge.id} className="h-auto" colSpan={showTime ? 2 : 1}>
                  <div className="text-center font-medium pb-2">
                    {judge.code}
                  </div>
                  <div className="flex border-t border-border">
                    <div className="flex-1 border-r border-border py-2 text-center">JID</div>
                    {showTime && <div className="flex-1 py-2 text-center">TIME</div>}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.rows?.map((row, index: number) => (
              <TableRow key={index}>
                <TableCell className="text-center font-medium">{index + 1}</TableCell>
                {(data?.judges || initialJudges).map((judge) => {
                  const judgeData = row[judge.id.toString()];
                  return (
                    <Fragment key={judge.id}>
                      <TableCell
                        className={cn(
                          "text-center border-l border-border",
                          judgeData?.isSequenceBreak && "bg-destructive/10 dark:bg-destructive/20"
                        )}
                      >
                        {judgeData?.jid || '-'}
                      </TableCell>
                      {showTime && (
                        <TableCell className="text-center border-l border-border">
                          {judgeData?.time || '-'}
                        </TableCell>
                      )}
                    </Fragment>
                  );
                })}
              </TableRow>
            ))}
            {(!data?.rows || data.rows.length === 0) && (
              <TableRow>
                <TableCell
                  colSpan={initialJudges.length * (showTime ? 2 : 1) + 1}
                  className="h-24 text-center text-muted-foreground"
                >
                  No judging data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Error message */}
      {error && (
        <div className="p-4 text-center text-red-500 bg-red-50 dark:bg-red-900/20 rounded-md">
          Error loading judging data. Please try again.
        </div>
      )}
    </div>
  );
}