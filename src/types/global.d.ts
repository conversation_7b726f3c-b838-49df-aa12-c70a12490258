// Type declarations for modules without TypeScript definitions

declare module 'clsx' {
  import { ClassValue } from 'clsx';
  export default function clsx(...inputs: ClassValue[]): string;
  export type { ClassValue };
}

declare module 'tailwind-merge' {
  export function twMerge(...inputs: string[]): string;
}

declare module '@radix-ui/react-checkbox' {
  import * as React from 'react';
  
  export interface CheckboxProps extends React.ComponentPropsWithoutRef<'button'> {
    checked?: boolean;
    defaultChecked?: boolean;
    onCheckedChange?(checked: boolean): void;
    disabled?: boolean;
    required?: boolean;
    name?: string;
    value?: string;
  }
  
  export const Root: React.ForwardRefExoticComponent<
    CheckboxProps & React.RefAttributes<HTMLButtonElement>
  >;
  
  export const Indicator: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'span'> & React.RefAttributes<HTMLSpanElement>
  >;
}

declare module '@radix-ui/react-select' {
  import * as React from 'react';
  
  export interface SelectProps extends React.ComponentPropsWithoutRef<'div'> {
    defaultValue?: string;
    value?: string;
    onValueChange?(value: string): void;
    open?: boolean;
    defaultOpen?: boolean;
    onOpenChange?(open: boolean): void;
    dir?: 'ltr' | 'rtl';
    name?: string;
    disabled?: boolean;
    required?: boolean;
  }
  
  export const Root: React.FC<SelectProps>;
  
  export const Trigger: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'button'> & React.RefAttributes<HTMLButtonElement>
  >;
  
  export const Value: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'span'> & React.RefAttributes<HTMLSpanElement>
  >;
  
  export const Icon: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'span'> & { asChild?: boolean } & React.RefAttributes<HTMLSpanElement>
  >;
  
  export const Portal: React.FC<React.ComponentPropsWithoutRef<'div'>>;
  
  export const Content: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'div'> & {
      position?: 'popper' | 'item';
    } & React.RefAttributes<HTMLDivElement>
  >;
  
  export const Viewport: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'div'> & React.RefAttributes<HTMLDivElement>
  >;
  
  export const Group: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'div'> & React.RefAttributes<HTMLDivElement>
  >;
  
  export const Label: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'div'> & React.RefAttributes<HTMLDivElement>
  >;
  
  export const Item: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'div'> & {
      value: string;
      disabled?: boolean;
    } & React.RefAttributes<HTMLDivElement>
  >;
  
  export const ItemText: React.FC<React.ComponentPropsWithoutRef<'span'>>;
  
  export const ItemIndicator: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'span'> & React.RefAttributes<HTMLSpanElement>
  >;
  
  export const Separator: React.ForwardRefExoticComponent<
    React.ComponentPropsWithoutRef<'div'> & React.RefAttributes<HTMLDivElement>
  >;
}

declare module '@radix-ui/react-slot' {
  import * as React from 'react';
  
  export interface SlotProps extends React.HTMLAttributes<HTMLElement> {
    asChild?: boolean;
  }
  
  export const Slot: React.ForwardRefExoticComponent<
    SlotProps & React.RefAttributes<HTMLElement>
  >;
}

declare module 'lucide-react' {
  import * as React from 'react';
  
  export interface IconProps extends React.SVGAttributes<SVGElement> {
    size?: number | string;
    absoluteStrokeWidth?: boolean;
  }
  
  export type Icon = React.ForwardRefExoticComponent<
    IconProps & React.RefAttributes<SVGSVGElement>
  >;
  
  export const Check: Icon;
  export const ChevronDown: Icon;
  export const RefreshCw: Icon;
  export const CheckIcon: Icon;
  export const Moon: Icon;
  export const Sun: Icon;
}

declare module 'class-variance-authority' {
  export type VariantProps<T extends (...args: unknown[]) => unknown> = Parameters<T>[0];
  
  export function cva(
    base: string,
    config: {
      variants?: Record<string, Record<string, string>>;
      defaultVariants?: Record<string, string>;
      compoundVariants?: Array<Record<string, string> & { class: string }>;
    }
  ): (props?: Record<string, string | number | boolean | undefined>) => string;
}

declare module 'swr' {
  export interface SWRConfiguration<Data = unknown> {
    refreshInterval?: number;
    fallbackData?: Data;
    revalidateOnFocus?: boolean;
    [key: string]: unknown;
  }
  
  export interface SWRResponse<Data = unknown, Error = Error> {
    data?: Data;
    error?: Error;
    mutate: (data?: Data, shouldRevalidate?: boolean) => Promise<Data | undefined>;
    isValidating: boolean;
  }
  
  export default function useSWR<Data = unknown, Error = Error>(
    key: string | null,
    fetcher: (key: string) => Promise<Data>,
    config?: SWRConfiguration<Data>
  ): SWRResponse<Data, Error>;
}