'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'

const ClientWrapper = dynamic(() => import('./page-client'), {
  ssr: false
})

export default function JudgingClientWrapper({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return <ClientWrapper>{children}</ClientWrapper>
}
