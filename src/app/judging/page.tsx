import { Suspense } from 'react'
import { getJudges, getCategories, getRounds, getJudgingRecords, transformJudgingData } from '@/lib/data'
import JudgingMonitor from '@/components/judging-monitor'
import JudgingPageClient from './page-client'

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default async function JudgingPage() {
  // Get the event ID from environment variable
  const eventId = parseInt(process.env.EVENT_ID || '0');
  
  if (!eventId) {
    return (
      <div className="p-8">
        <div className="p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md">
          Error: EVENT_ID is not configured in .env.local
        </div>
      </div>
    );
  }

  try {
    // Fetch initial data
    const [judges, categories, rounds, judgingRecords] = await Promise.all([
      getJudges(eventId), // Initial load gets all judges
      getCategories(eventId),
      getRounds(eventId),
      getJudgingRecords(eventId),
    ]);

    // Transform judging records for the UI
    const initialJudgingData = transformJudgingData(judgingRecords, judges);

    return (
      <Suspense fallback={<div>Loading judging data...</div>}>
        <JudgingPageClient>
          <JudgingMonitor
            eventId={eventId}
            initialCategories={categories}
            initialRounds={rounds}
            initialJudges={judges}
            initialJudgingData={initialJudgingData}
          />
        </JudgingPageClient>
      </Suspense>
    );
  } catch (error) {
    console.error('Error loading judging page:', error);
    return (
      <div className="p-8">
        <div className="p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md">
          Error loading judging data. Please check your database connection and try again.
        </div>
      </div>
    );
  }
}