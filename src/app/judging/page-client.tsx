'use client'

import { useAuth } from '@/context/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'

type Props = {
  children: React.ReactNode
}

export default function JudgingPageClient({ children }: Props) {
  const { isAuthenticated, logout } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/')
    }
  }, [isAuthenticated, router])

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background text-foreground p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Judging Monitoring</h1>
            <p className="text-muted-foreground">Real-time judging progress tracker</p>
          </div>
          <div className="flex gap-4">
            <Button
              variant="secondary"
              onClick={() => router.push('/dashboard')}
            >
              Dashboard
            </Button>
            <Button
              variant="destructive"
              onClick={handleLogout}
            >
              Logout
            </Button>
          </div>
        </div>
        <div className="rounded-lg border bg-card p-6">
          {children}
        </div>
      </div>
    </div>
  );
}