'use client';

import { useAuth } from "@/context/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Dashboard() {
  const { isAuthenticated, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, router]);

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white p-8 transition-colors duration-200">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold">Judging Monitoring Dashboard</h1>
          <div className="flex gap-4">
            <button
              onClick={() => router.push('/judging')}
              className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
            >
              Judging Monitor
            </button>
            <button
              onClick={() => router.push('/categories')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Categories
            </button>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg transition-colors duration-200">
          <p className="text-gray-600 dark:text-gray-300">Welcome to the dashboard!</p>
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer" onClick={() => router.push('/judging')}>
              <h2 className="text-lg font-medium mb-2">Judging Monitor</h2>
              <p className="text-gray-500 dark:text-gray-400">View real-time judging progress across all judges.</p>
            </div>
            <div className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer" onClick={() => router.push('/categories')}>
              <h2 className="text-lg font-medium mb-2">Categories</h2>
              <p className="text-gray-500 dark:text-gray-400">View and manage all available categories.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
