import { NextRequest, NextResponse } from 'next/server';
import {
  getJudges,
  getJudgingRecords,
  transformJudgingData,
} from '@/lib/data';

export async function GET(request: NextRequest) {
  try {
    // Get the event ID from environment variable
    const eventId = parseInt(process.env.EVENT_ID || '0');
    if (!eventId) {
      return NextResponse.json(
        { error: 'EVENT_ID is not configured' },
        { status: 500 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const categoryId = searchParams.get('categoryId');
    const roundId = searchParams.get('roundId');

    // Fetch judges and judging records
    const judges = await getJudges(
      eventId,
      categoryId ? parseInt(categoryId) : undefined
    );
    const judgingRecords = await getJudgingRecords(
      eventId,
      categoryId ? parseInt(categoryId) : undefined,
      roundId ? parseInt(roundId) : undefined
    );

    // Transform the data for the UI
    const transformedData = transformJudgingData(judgingRecords, judges);

    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('Error in judging API route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch judging data' },
      { status: 500 }
    );
  }
}