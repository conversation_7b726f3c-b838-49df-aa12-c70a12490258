import { NextRequest, NextResponse } from 'next/server';
import { getCategories } from '@/lib/data';

export async function GET(request: NextRequest) {
  try {
    // Get the event ID from environment variable
    const eventId = parseInt(process.env.EVENT_ID || '0');
    if (!eventId) {
      return NextResponse.json(
        { error: 'EVENT_ID is not configured' },
        { status: 500 }
      );
    }

    // Fetch categories
    const categories = await getCategories(eventId);

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error in categories API route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories data' },
      { status: 500 }
    );
  }
}