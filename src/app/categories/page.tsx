import CategoryList from '@/components/CategoryList';
import { Suspense } from 'react';

export default function CategoriesPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white p-8 transition-colors duration-200">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold">Categories</h1>
          <p className="text-gray-600 dark:text-gray-400">View all available categories</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg transition-colors duration-200">
          <Suspense fallback={<div>Loading categories...</div>}>
            <CategoryList />
          </Suspense>
        </div>
      </div>
    </div>
  );
}