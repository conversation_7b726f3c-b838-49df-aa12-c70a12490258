CREATE TABLE `events_judging` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `circuit_id` bigint unsigned NOT NULL,
  `jury_id` bigint unsigned NOT NULL,
  `round_id` bigint unsigned NOT NULL,
  `submission_id` bigint unsigned NOT NULL,
  `score` tinyint unsigned NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'note when jury give minimal score',
  `ask_for_raw` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'flag when jury need raw photo',
  `disqualified` tinyint(1) DEFAULT NULL COMMENT 'not null is disqualified',
  `time_created` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_circuit_jury_round_submission` (`circuit_id`,`jury_id`,`round_id`,`submission_id`),
  KEY `circuit_id` (`circuit_id`),
  <PERSON><PERSON>Y `jury_id` (`jury_id`),
  KEY `round_id` (`round_id`),
  KEY `submission_id` (`submission_id`),
  CONSTRAINT `events_judging_ibfk_1` FOREIGN KEY (`circuit_id`) REFERENCES `events_circuits` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_judging_ibfk_2` FOREIGN KEY (`jury_id`) REFERENCES `events_juries` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_judging_ibfk_3` FOREIGN KEY (`round_id`) REFERENCES `events_rounds` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_judging_ibfk_4` FOREIGN KEY (`submission_id`) REFERENCES `events_submissions` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
);

CREATE TABLE `events_juries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'jury_id',
  `event_id` bigint unsigned NOT NULL,
  `person_id` bigint unsigned DEFAULT NULL COMMENT 'ini belum tahu bakal dipakai apa tidak',
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'cache: jury name',
  `code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `time_confirmation` int unsigned DEFAULT NULL COMMENT 'null is not confirmed',
  `time_created` int unsigned NOT NULL DEFAULT '0',
  `time_updated` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_event_jury_code` (`event_id`,`code`),
  UNIQUE KEY `unique_event_jury_person` (`event_id`,`person_id`),
  KEY `person_id` (`person_id`),
  CONSTRAINT `events_juries_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_juries_ibfk_2` FOREIGN KEY (`person_id`) REFERENCES `persons` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ;

CREATE TABLE `events_categories_jury` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `circuit_id` bigint unsigned NOT NULL,
  `category_id` bigint unsigned NOT NULL,
  `jury_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_jury_category` (`circuit_id`,`category_id`,`jury_id`),
  KEY `category_id` (`category_id`),
  KEY `jury_id` (`jury_id`),
  CONSTRAINT `events_categories_jury_ibfk_1` FOREIGN KEY (`circuit_id`) REFERENCES `events_circuits` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_categories_jury_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `events_categories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `events_categories_jury_ibfk_3` FOREIGN KEY (`jury_id`) REFERENCES `events_juries` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) 

CREATE TABLE `events_rounds` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'round_id',
  `event_id` bigint unsigned NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `position` tinyint unsigned NOT NULL DEFAULT '1',
  `min_score` int unsigned NOT NULL DEFAULT '3' COMMENT 'min score given by jury',
  `max_score` int unsigned NOT NULL DEFAULT '9' COMMENT 'max score given by jury',
  `features` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'json: features',
  `time_created` int unsigned NOT NULL DEFAULT '0',
  `time_updated` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `event_id` (`event_id`),
  CONSTRAINT `events_rounds_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE `events_submissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'submission_id',
  `participant_id` bigint unsigned NOT NULL COMMENT 'FK: events_participants',
  `category_id` bigint unsigned NOT NULL COMMENT 'FK: events_categories',
  `slot` tinyint unsigned NOT NULL COMMENT 'photo slot position each category',
  `jid` int unsigned DEFAULT NULL COMMENT 'photo number on judging',
  `participate_special_award` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '1=yes, 0=no [flag only]',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` enum('image','video') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'image' COMMENT 'type of uploaded file',
  `file_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'original file of uploaded file',
  `image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `image_url_thumb` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'image thumbnail: highest resolution',
  `image_url_300` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'image thumbnail: max x/y 300px',
  `image_url_200` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'image thumbnail: max x/y 200px',
  `organizer_id_disqualify` bigint unsigned DEFAULT NULL COMMENT 'user who gave disqualification',
  `disqualification` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'disqualification status, if NOT NULL image disqualified',
  `similarity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'json data similarity photo, NULL or [] = no similary',
  `time_created` int unsigned NOT NULL DEFAULT '0',
  `time_updated` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_slot_category` (`category_id`,`slot`,`participant_id`),
  UNIQUE KEY `unique_jid_by_category` (`category_id`,`jid`),
  KEY `participant_id` (`participant_id`),
  KEY `events_submissions_ibfk_3` (`organizer_id_disqualify`),
  CONSTRAINT `events_submissions_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `events_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `events_submissions_ibfk_2` FOREIGN KEY (`participant_id`) REFERENCES `events_participants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `events_submissions_ibfk_3` FOREIGN KEY (`organizer_id_disqualify`) REFERENCES `events_organizers` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ;

CREATE TABLE `events_circuits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'circuit_id',
  `event_id` bigint unsigned NOT NULL,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `time_created` int unsigned NOT NULL DEFAULT '0',
  `time_updated` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `event_id` (`event_id`),
  CONSTRAINT `events_circuits_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ;

CREATE TABLE `events_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'category_id',
  `event_id` bigint unsigned NOT NULL,
  `code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `has_special_award` enum('no','yes') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'no',
  `type` enum('digital','print') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'digital' COMMENT '`print` can generate barcode label',
  `max_submit` tinyint unsigned NOT NULL DEFAULT '4',
  `position` tinyint unsigned NOT NULL DEFAULT '1',
  `time_created` int unsigned NOT NULL DEFAULT '0',
  `time_updated` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_category_code` (`event_id`,`code`),
  CONSTRAINT `events_categories_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);